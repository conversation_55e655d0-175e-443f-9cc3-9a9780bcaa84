#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信自动化程序打包脚本
专门用于将Python程序打包为Windows可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def clean_build_dirs():
    """清理之前的构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✅ 清理目录: {dir_name}")

def build_gui_app():
    """打包GUI应用程序"""
    print("🚀 开始打包微信自动化GUI程序...")
    
    # 排除的大型库列表
    excludes = [
        'torch', 'torchvision', 'tensorflow', 'sklearn', 'scipy', 'matplotlib', 
        'sympy', 'paddle', 'cv2', 'IPython', 'jupyter', 'notebook', 'jedi', 
        'parso', 'lxml', 'cryptography', 'bcrypt', 'pydantic', 'anyio', 
        'uvicorn', 'regex', 'transformers', 'onnxruntime', 'fsspec', 
        'sqlalchemy', 'psutil', 'seaborn', 'plotly', 'bokeh'
    ]
    
    # 构建排除参数
    exclude_args = []
    for lib in excludes:
        exclude_args.extend(['--exclude-module', lib])
    
    # PyInstaller命令
    cmd = [
        'python', '-m', 'PyInstaller',
        '--onefile',
        '--windowed',
        '--name=微信自动化GUI',
        '--add-data=modules;modules',
        '--add-data=config.json;.',
        '--add-data=window_safety_config.json;.',
        '--add-data=添加好友名单.xlsx;.',
        '--hidden-import=tkinter',
        '--hidden-import=tkinter.ttk',
        '--hidden-import=tkinter.messagebox',
        '--hidden-import=tkinter.filedialog',
        '--hidden-import=PIL',
        '--hidden-import=PIL.Image',
        '--hidden-import=PIL.ImageTk',
        '--hidden-import=pandas',
        '--hidden-import=openpyxl',
        '--hidden-import=pyautogui',
        '--hidden-import=win32gui',
        '--hidden-import=win32api',
        '--hidden-import=pytesseract',
        '--clean'
    ] + exclude_args + ['wechat_automation_gui.py']
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ GUI程序打包成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ GUI程序打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def build_controller_app():
    """打包控制器程序"""
    print("🚀 开始打包主控制器程序...")
    
    # 排除的大型库列表
    excludes = [
        'torch', 'torchvision', 'tensorflow', 'sklearn', 'scipy', 'matplotlib', 
        'sympy', 'paddle', 'cv2', 'IPython', 'jupyter', 'notebook', 'jedi', 
        'parso', 'lxml', 'cryptography', 'bcrypt', 'pydantic', 'anyio', 
        'uvicorn', 'regex', 'transformers', 'onnxruntime', 'fsspec', 
        'sqlalchemy', 'psutil', 'seaborn', 'plotly', 'bokeh'
    ]
    
    # 构建排除参数
    exclude_args = []
    for lib in excludes:
        exclude_args.extend(['--exclude-module', lib])
    
    # PyInstaller命令
    cmd = [
        'python', '-m', 'PyInstaller',
        '--onefile',
        '--console',
        '--name=微信自动化控制器',
        '--add-data=modules;modules',
        '--add-data=config.json;.',
        '--add-data=window_safety_config.json;.',
        '--add-data=添加好友名单.xlsx;.',
        '--hidden-import=pandas',
        '--hidden-import=openpyxl',
        '--hidden-import=pyautogui',
        '--hidden-import=win32gui',
        '--hidden-import=win32api',
        '--hidden-import=pytesseract',
        '--clean'
    ] + exclude_args + ['main_controller.py']
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 控制器程序打包成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 控制器程序打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def build_config_app():
    """打包配置程序"""
    print("🚀 开始打包配置对话框程序...")
    
    # 排除的大型库列表
    excludes = [
        'torch', 'torchvision', 'tensorflow', 'sklearn', 'scipy', 'matplotlib', 
        'sympy', 'paddle', 'cv2', 'IPython', 'jupyter', 'notebook', 'jedi', 
        'parso', 'lxml', 'cryptography', 'bcrypt', 'pydantic', 'anyio', 
        'uvicorn', 'regex', 'transformers', 'onnxruntime', 'fsspec', 
        'sqlalchemy', 'psutil', 'seaborn', 'plotly', 'bokeh'
    ]
    
    # 构建排除参数
    exclude_args = []
    for lib in excludes:
        exclude_args.extend(['--exclude-module', lib])
    
    # PyInstaller命令
    cmd = [
        'python', '-m', 'PyInstaller',
        '--onefile',
        '--windowed',
        '--name=微信配置工具',
        '--hidden-import=tkinter',
        '--hidden-import=tkinter.ttk',
        '--hidden-import=tkinter.messagebox',
        '--clean'
    ] + exclude_args + ['config_dialog.py']
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 配置程序打包成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 配置程序打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def create_distribution_package():
    """创建分发包"""
    print("📦 创建分发包...")
    
    dist_dir = Path("微信7.28自动化工具包")
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    
    dist_dir.mkdir()
    
    # 复制exe文件
    dist_path = Path("dist")
    if dist_path.exists():
        for exe_file in dist_path.glob("*.exe"):
            shutil.copy2(exe_file, dist_dir)
            print(f"✅ 复制文件: {exe_file.name}")
    
    # 复制配置文件
    config_files = [
        "config.json", "window_safety_config.json", 
        "添加好友名单.xlsx", "requirements.txt"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            shutil.copy2(config_file, dist_dir)
            print(f"✅ 复制配置: {config_file}")
    
    print(f"✅ 分发包创建完成: {dist_dir}")

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 微信自动化程序打包工具")
    print("=" * 60)
    
    # 清理构建目录
    clean_build_dirs()
    
    success_count = 0
    total_count = 3
    
    # 打包各个程序
    if build_gui_app():
        success_count += 1
    
    if build_controller_app():
        success_count += 1
        
    if build_config_app():
        success_count += 1
    
    # 创建分发包
    if success_count > 0:
        create_distribution_package()
    
    print("=" * 60)
    print(f"📊 打包结果: {success_count}/{total_count} 个程序打包成功")
    print("=" * 60)
    
    if success_count == total_count:
        print("🎉 所有程序打包完成!")
    else:
        print("⚠️ 部分程序打包失败，请检查错误信息")

if __name__ == "__main__":
    main()
