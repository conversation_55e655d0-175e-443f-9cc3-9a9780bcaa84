# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['config_dialog.py'],
    pathex=['C:\Users\<USER>\Desktop\微信7.28 - 副本 (2)'],
    binaries=[],
    datas=[
        ('C:\Users\<USER>\Desktop\微信7.28 - 副本 (2)\modules', 'modules'),('C:\Users\<USER>\Desktop\微信7.28 - 副本 (2)\config.json', '.'),('C:\Users\<USER>\Desktop\微信7.28 - 副本 (2)\window_safety_config.json', '.'),('C:\Users\<USER>\Desktop\微信7.28 - 副本 (2)\添加好友名单.xlsx', '.'),('C:\Users\<USER>\Desktop\微信7.28 - 副本 (2)\requirements.txt', '.')
    ],
    hiddenimports=['tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.filedialog', 'tkinter.scrolledtext', 'PIL', 'PIL.Image', 'PIL.ImageTk', 'numpy', 'pandas', 'openpyxl', 'pyautogui', 'pywin32', 'win32gui', 'win32con', 'win32api', 'win32process', 'pytesseract', 'modules', 'modules.config_utils', 'modules.data_manager', 'modules.frequency_error_handler', 'modules.friend_request_window', 'modules.log_deduplication_manager', 'modules.main_interface', 'modules.mouse_visual_feedback', 'modules.wechat_auto_add_friend', 'modules.wechat_auto_add_simple', 'modules.window_manager'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='config_dialog',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
