# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['main_controller.py'],
    pathex=[],
    binaries=[],
    datas=[('modules', 'modules'), ('config.json', '.'), ('window_safety_config.json', '.'), ('添加好友名单.xlsx', '.')],
    hiddenimports=['pandas', 'openpyxl', 'pyautogui', 'win32gui', 'win32api', 'pytesseract'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['torch', 'torchvision', 'tensorflow', 'sklearn', 'scipy', 'matplotlib', 'sympy', 'paddle', 'cv2', 'IPython', 'jupyter', 'notebook', 'jedi', 'parso', 'lxml', 'cryptography', 'bcrypt', 'pydantic', 'anyio', 'uvicorn', 'regex', 'transformers', 'onnxruntime', 'fsspec', 'sqlalchemy', 'psutil', 'seaborn', 'plotly', 'bokeh'],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='微信自动化控制器',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
